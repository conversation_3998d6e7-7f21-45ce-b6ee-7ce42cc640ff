'use client'

interface AvailabilityItem {
  id: string;
  name: string;
  icon: string;
  available: boolean;
  color: string;
}

interface AvailabilityProps {
  items: AvailabilityItem[];
}

export default function Availability({ items }: AvailabilityProps) {
  return (
    <div className="fixed top-4 right-72 z-50">
      <div className="glass-effect rounded-lg overflow-hidden w-72">
        {/* Header */}
        <div className="section-header">
          AVAILABILITY
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="grid grid-cols-4 gap-3">
            {items.map((item) => (
              <div
                key={item.id}
                className={`availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square ${
                  item.available ? 'opacity-100' : 'opacity-40'
                }`}
                style={{
                  backgroundColor: item.available ? `${item.color}15` : 'rgba(38, 38, 38, 0.6)',
                  borderColor: item.available ? `${item.color}40` : 'rgba(255, 255, 255, 0.1)'
                }}
              >
                <div
                  className="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg"
                  style={{ backgroundColor: item.available ? item.color : '#555555' }}
                >
                  <i className={`${item.icon} text-white text-lg`}></i>
                </div>

                <span className="text-light-100 text-xs font-semibold text-center leading-tight">
                  {item.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
