'use client'

interface AvailabilityItem {
  id: string;
  name: string;
  icon: string;
  available: boolean;
  color: string;
}

interface AvailabilityProps {
  items: AvailabilityItem[];
}

export default function Availability({ items }: AvailabilityProps) {
  return (
    <div className="fixed top-4 right-72 z-50">
      <div className="glass-effect rounded-lg overflow-hidden w-64">
        {/* Header */}
        <div className="section-header">
          AVAILABILITY
        </div>

        {/* Content */}
        <div className="p-3">
          <div className="grid grid-cols-4 gap-2">
            {items.map((item) => (
              <div
                key={item.id}
                className={`availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square ${
                  item.available ? 'opacity-100' : 'opacity-50'
                }`}
              >
                <div
                  className="w-8 h-8 rounded flex items-center justify-center"
                  style={{ backgroundColor: item.available ? item.color : '#555555' }}
                >
                  <i className={`${item.icon} text-white text-sm`}></i>
                </div>

                <span className="text-white text-xs font-medium text-center leading-tight">
                  {item.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
