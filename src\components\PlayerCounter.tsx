'use client'

interface PlayerCounterProps {
  currentPlayers: number;
}

export default function PlayerCounter({ currentPlayers }: PlayerCounterProps) {
  return (
    <div className="fixed top-6 left-6 z-50">
      <div className="flex items-center gap-3">
        <span
          className="text-primary font-bold text-6xl"
          style={{ textShadow: '2px 2px 4px #C73659' }}
        >
          {currentPlayers}
        </span>
        <i
          className="fas fa-users text-primary text-4xl"
          style={{ textShadow: '2px 2px 4px #C73659' }}
        ></i>
      </div>
    </div>
  )
}
