'use client'

interface PlayerCounterProps {
  currentPlayers: number;
}

export default function PlayerCounter({ currentPlayers }: PlayerCounterProps) {
  return (
    <div className="fixed top-4 left-4 z-50">
      <div className="flex items-center gap-3">
        <i className="fas fa-users text-primary text-4xl"></i>
        <span className="text-primary font-bold text-6xl">{currentPlayers}</span>
      </div>
    </div>
  )
}
