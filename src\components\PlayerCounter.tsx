'use client'

interface PlayerCounterProps {
  currentPlayers: number;
  maxPlayers?: number;
}

export default function PlayerCounter({ currentPlayers, maxPlayers = 64 }: PlayerCounterProps) {
  return (
    <div className="fixed top-4 left-4 z-50">
      <div className="glass-effect rounded-lg px-6 py-3 flex items-center gap-3 shadow-lg">
        <i className="fas fa-users text-primary text-2xl"></i>
        <span className="text-primary font-bold text-3xl">{currentPlayers}</span>
        {maxPlayers && (
          <span className="text-light-200 text-lg font-medium">/ {maxPlayers}</span>
        )}
      </div>
    </div>
  )
}
