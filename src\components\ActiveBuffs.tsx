'use client'

interface Buff {
  id: string;
  name: string;
  icon: string;
  duration: string;
  multiplier: string;
  type: 'positive' | 'negative';
}

interface ActiveBuffsProps {
  buffs: Buff[];
}

export default function ActiveBuffs({ buffs }: ActiveBuffsProps) {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="glass-effect rounded-lg overflow-hidden w-96">
        {/* Header */}
        <div className="section-header">
          ACTIVE BUFFS
        </div>

        {/* Content */}
        <div className="p-4 space-y-3">
          {buffs.map((buff) => (
            <div
              key={buff.id}
              className="buff-item rounded-lg p-4 flex items-center gap-4"
            >
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center shadow-lg ${
                buff.type === 'positive' ? 'bg-green-600' : 'bg-orange-600'
              }`}>
                <i className={`${buff.icon} text-white text-xl`}></i>
              </div>

              <div className="flex-1">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-light-100 text-sm font-semibold">
                    {buff.name}
                  </span>
                  <span className="text-light-200 text-sm font-medium">
                    {buff.duration}
                  </span>
                </div>

                <div className="flex justify-end">
                  <span className={`text-sm font-bold ${
                    buff.type === 'positive' ? 'text-green-400' : 'text-orange-400'
                  }`}>
                    {buff.multiplier}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
