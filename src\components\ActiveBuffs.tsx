'use client'

interface Buff {
  id: string;
  name: string;
  icon: string;
  duration: string;
  multiplier: string;
  type: 'positive' | 'negative';
}

interface ActiveBuffsProps {
  buffs: Buff[];
}

export default function ActiveBuffs({ buffs }: ActiveBuffsProps) {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="glass-effect rounded-lg overflow-hidden w-80">
        {/* Header */}
        <div className="section-header">
          ACTIVE BUFFS
        </div>

        {/* Content */}
        <div className="p-3 space-y-2">
          {/* No Active Buffs */}
          <div className="no-buffs rounded text-xs">
            No Active Buffs :(
          </div>

          {/* Active Buffs */}
          {buffs.map((buff) => (
            <div
              key={buff.id}
              className="buff-item rounded p-3 flex items-center gap-3"
            >
              <div
                className="w-10 h-10 rounded flex items-center justify-center"
                style={{ backgroundColor: '#C73659' }}
              >
                <i className={`${buff.icon} text-white text-sm`}></i>
              </div>

              <div className="flex-1">
                <div className="flex justify-between items-center">
                  <span className="text-white text-sm font-medium">
                    {buff.name}
                  </span>
                  <span className="text-white text-sm">
                    {buff.duration}
                  </span>
                </div>
                <div className="flex justify-end mt-1">
                  <span className="text-sm font-bold" style={{ color: '#C73659' }}>
                    {buff.multiplier}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
