'use client'

import PlayerCounter from '@/components/PlayerCounter'
import PlayerInfo from '@/components/PlayerInfo'
import Activities from '@/components/Activities'
import Availability from '@/components/Availability'
import ActiveBuffs from '@/components/ActiveBuffs'

// Sample data - في التطبيق الحقيقي ستأتي من API أو FiveM
const playerData = {
  nationality: 'USA',
  birthday: '2000/1/1',
  job: 'Law Enforcement',
  playtime: '106 hours',
  paycheck: 14320,
  bank: 200,
  cash: 450,
  coins: 300,
  insurance: 'No Insurance'
}

const activities = [
  { id: '1', name: 'Bank Robbery', icon: 'fas fa-university', available: true },
  { id: '2', name: 'Store Robbery', icon: 'fas fa-store', available: false },
  { id: '3', name: 'House Robbery', icon: 'fas fa-home', available: true },
  { id: '4', name: 'Drug Dealing', icon: 'fas fa-pills', available: true },
  { id: '5', name: '<PERSON> Theft', icon: 'fas fa-car', available: false },
  { id: '6', name: 'Jewelry Heist', icon: 'fas fa-gem', available: true }
]

const availabilityItems = [
  { id: '1', name: 'LEO KIDNAP', icon: 'fas fa-handcuffs', available: true, color: '#3B82F6' },
  { id: '2', name: 'STORES', icon: 'fas fa-store', available: true, color: '#EF4444' },
  { id: '3', name: 'POLICE', icon: 'fas fa-shield-alt', available: false, color: '#10B981' },
  { id: '4', name: 'SHERIFF', icon: 'fas fa-star', available: true, color: '#F59E0B' },
  { id: '5', name: 'HOUSES', icon: 'fas fa-home', available: true, color: '#F97316' },
  { id: '6', name: 'EXCHANGE', icon: 'fas fa-exchange-alt', available: false, color: '#8B5CF6' },
  { id: '7', name: 'HOSPITAL', icon: 'fas fa-hospital', available: true, color: '#06B6D4' },
  { id: '8', name: 'JEWELRY', icon: 'fas fa-gem', available: false, color: '#84CC16' }
]

const activeBuffs = [
  {
    id: '1',
    name: 'Post OP',
    icon: 'fas fa-truck',
    duration: '15m',
    multiplier: '3x',
    type: 'positive' as const
  },
  {
    id: '2',
    name: "Uncle Damby's Farms",
    icon: 'fas fa-tractor',
    duration: '6m',
    multiplier: '2x',
    type: 'positive' as const
  }
]

export default function Home() {
  return (
    <main className="min-h-screen relative overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')",
        }}
      >
        <div className="absolute inset-0 bg-black/50"></div>
      </div>

      {/* UI Components */}
      <PlayerCounter currentPlayers={1} />
      <PlayerInfo playerData={playerData} />
      <Activities activities={activities} />
      <Availability items={availabilityItems} />
      <ActiveBuffs buffs={activeBuffs} />
    </main>
  )
}
