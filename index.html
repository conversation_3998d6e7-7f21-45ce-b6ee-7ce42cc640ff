<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Info - FiveM UI</title>
    <link href="https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#C73659',
                        dark: {
                            100: '#262626',
                            200: '#111111',
                        },
                        light: {
                            100: '#E2E2E2',
                            200: '#D7D7D7',
                        }
                    },
                    fontFamily: {
                        'alexandria': ['Alexandria', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Alexandria', sans-serif;
            background-color: #111111;
            color: #E2E2E2;
            overflow: hidden;
            user-select: none;
        }

        .glass-effect {
            background: rgba(17, 17, 17, 0.85);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(199, 54, 89, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .section-header {
            background: #C73659;
            color: white;
            font-weight: 700;
            text-align: center;
            padding: 10px 16px;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 1px solid rgba(199, 54, 89, 0.5);
        }

        .activity-item {
            background: rgba(38, 38, 38, 0.7);
            border: 1px solid rgba(199, 54, 89, 0.2);
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            border-color: rgba(199, 54, 89, 0.6);
            background: rgba(199, 54, 89, 0.1);
            transform: translateY(-1px);
        }

        .buff-item {
            background: rgba(38, 38, 38, 0.8);
            border-left: 4px solid #C73659;
            transition: all 0.2s ease;
        }

        .buff-item:hover {
            background: rgba(199, 54, 89, 0.1);
            transform: translateX(2px);
        }

        .availability-item {
            background: rgba(38, 38, 38, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease;
        }

        .availability-item:hover {
            transform: scale(1.05);
            border-color: rgba(199, 54, 89, 0.4);
        }

        .bg-game {
            background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>
<body class="font-alexandria bg-dark-200 text-light-100">
    <main class="min-h-screen relative overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 bg-game">
            <div class="absolute inset-0 bg-black/50"></div>
        </div>

        <!-- Player Counter -->
        <div class="fixed top-4 left-4 z-50">
            <div class="glass-effect rounded-lg px-6 py-3 flex items-center gap-3 shadow-lg">
                <i class="fas fa-users text-primary text-2xl"></i>
                <span class="text-primary font-bold text-3xl">1</span>
                <span class="text-light-200 text-lg font-medium">/ 64</span>
            </div>
        </div>

        <!-- Player Info -->
        <div class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
            <div class="glass-effect rounded-lg overflow-hidden min-w-[320px]">
                <!-- Header -->
                <div class="section-header">
                    SST Dev
                </div>
                
                <!-- Content -->
                <div class="p-5 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Nationality:</span>
                        <span class="text-light-100 font-semibold">USA</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Birthday:</span>
                        <span class="text-light-100 font-semibold">20/02/1/1</span>
                    </div>
                </div>

                <!-- Details Section -->
                <div class="section-header">
                    Details
                </div>
                
                <div class="p-5 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Job:</span>
                        <span class="text-light-100 font-semibold">Law Enforcement</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Play Time:</span>
                        <span class="text-light-100 font-semibold">106 Hours</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Paycheck:</span>
                        <span class="text-green-400 font-bold">$14,520</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Bank:</span>
                        <span class="text-green-400 font-bold">$5,200</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Cash:</span>
                        <span class="text-green-400 font-bold">$1,450</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Coins:</span>
                        <span class="text-yellow-400 font-bold">300</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-light-200 text-sm font-medium">Insurance:</span>
                        <span class="text-red-400 font-semibold">No Insurance</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activities -->
        <div class="fixed top-4 right-4 z-50">
            <div class="glass-effect rounded-lg overflow-hidden w-72">
                <!-- Header -->
                <div class="section-header">
                    ACTIVITIES
                </div>
                
                <!-- Content -->
                <div class="p-4 space-y-3">
                    <div class="activity-item rounded-lg p-4 flex items-center gap-4 opacity-100">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg bg-green-600">
                            <i class="fas fa-university text-white text-base"></i>
                        </div>
                        <div class="flex-1">
                            <span class="text-light-100 text-sm font-semibold">Bank Robbery</span>
                        </div>
                        <div class="w-3 h-3 rounded-full shadow-sm bg-green-400"></div>
                    </div>
                    
                    <div class="activity-item rounded-lg p-4 flex items-center gap-4 opacity-60">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg bg-red-600">
                            <i class="fas fa-store text-white text-base"></i>
                        </div>
                        <div class="flex-1">
                            <span class="text-light-100 text-sm font-semibold">Store Robbery</span>
                        </div>
                        <div class="w-3 h-3 rounded-full shadow-sm bg-red-400"></div>
                    </div>
                    
                    <div class="activity-item rounded-lg p-4 flex items-center gap-4 opacity-100">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg bg-green-600">
                            <i class="fas fa-home text-white text-base"></i>
                        </div>
                        <div class="flex-1">
                            <span class="text-light-100 text-sm font-semibold">House Robbery</span>
                        </div>
                        <div class="w-3 h-3 rounded-full shadow-sm bg-green-400"></div>
                    </div>
                    
                    <div class="activity-item rounded-lg p-4 flex items-center gap-4 opacity-100">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg bg-green-600">
                            <i class="fas fa-pills text-white text-base"></i>
                        </div>
                        <div class="flex-1">
                            <span class="text-light-100 text-sm font-semibold">Drug Dealing</span>
                        </div>
                        <div class="w-3 h-3 rounded-full shadow-sm bg-green-400"></div>
                    </div>
                    
                    <div class="activity-item rounded-lg p-4 flex items-center gap-4 opacity-60">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg bg-red-600">
                            <i class="fas fa-car text-white text-base"></i>
                        </div>
                        <div class="flex-1">
                            <span class="text-light-100 text-sm font-semibold">Car Theft</span>
                        </div>
                        <div class="w-3 h-3 rounded-full shadow-sm bg-red-400"></div>
                    </div>
                    
                    <div class="activity-item rounded-lg p-4 flex items-center gap-4 opacity-100">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg bg-green-600">
                            <i class="fas fa-gem text-white text-base"></i>
                        </div>
                        <div class="flex-1">
                            <span class="text-light-100 text-sm font-semibold">Jewelry Heist</span>
                        </div>
                        <div class="w-3 h-3 rounded-full shadow-sm bg-green-400"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Availability -->
        <div class="fixed top-4 right-80 z-50">
            <div class="glass-effect rounded-lg overflow-hidden w-72">
                <!-- Header -->
                <div class="section-header">
                    AVAILABILITY
                </div>
                
                <!-- Content -->
                <div class="p-4">
                    <div class="grid grid-cols-4 gap-3">
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-100" style="background-color: #3B82F615; border-color: #3B82F640;">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #3B82F6;">
                                <i class="fas fa-handcuffs text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">LEO KIDNAP</span>
                        </div>
                        
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-100" style="background-color: #EF444415; border-color: #EF444440;">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #EF4444;">
                                <i class="fas fa-store text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">STORES</span>
                        </div>
                        
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-40" style="background-color: rgba(38, 38, 38, 0.6); border-color: rgba(255, 255, 255, 0.1);">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #555555;">
                                <i class="fas fa-shield-alt text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">POLICE</span>
                        </div>
                        
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-100" style="background-color: #F59E0B15; border-color: #F59E0B40;">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #F59E0B;">
                                <i class="fas fa-star text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">SHERIFF</span>
                        </div>
                        
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-100" style="background-color: #F9731615; border-color: #F9731640;">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #F97316;">
                                <i class="fas fa-home text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">HOUSES</span>
                        </div>
                        
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-40" style="background-color: rgba(38, 38, 38, 0.6); border-color: rgba(255, 255, 255, 0.1);">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #555555;">
                                <i class="fas fa-exchange-alt text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">EXCHANGE</span>
                        </div>
                        
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-100" style="background-color: #06B6D415; border-color: #06B6D440;">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #06B6D4;">
                                <i class="fas fa-hospital text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">HOSPITAL</span>
                        </div>
                        
                        <div class="availability-item rounded-lg p-3 flex flex-col items-center gap-2 aspect-square opacity-40" style="background-color: rgba(38, 38, 38, 0.6); border-color: rgba(255, 255, 255, 0.1);">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg" style="background-color: #555555;">
                                <i class="fas fa-gem text-white text-lg"></i>
                            </div>
                            <span class="text-light-100 text-xs font-semibold text-center leading-tight">JEWELRY</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Buffs -->
        <div class="fixed bottom-4 right-4 z-50">
            <div class="glass-effect rounded-lg overflow-hidden w-96">
                <!-- Header -->
                <div class="section-header">
                    ACTIVE BUFFS
                </div>
                
                <!-- Content -->
                <div class="p-4 space-y-3">
                    <div class="buff-item rounded-lg p-4 flex items-center gap-4">
                        <div class="w-12 h-12 rounded-lg flex items-center justify-center shadow-lg bg-green-600">
                            <i class="fas fa-truck text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-light-100 text-sm font-semibold">Post OP</span>
                                <span class="text-light-200 text-sm font-medium">15m</span>
                            </div>
                            <div class="flex justify-end">
                                <span class="text-sm font-bold text-green-400">3x</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="buff-item rounded-lg p-4 flex items-center gap-4">
                        <div class="w-12 h-12 rounded-lg flex items-center justify-center shadow-lg bg-green-600">
                            <i class="fas fa-tractor text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-light-100 text-sm font-semibold">Uncle Damby's Farms</span>
                                <span class="text-light-200 text-sm font-medium">6m</span>
                            </div>
                            <div class="flex justify-end">
                                <span class="text-sm font-bold text-green-400">2x</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
