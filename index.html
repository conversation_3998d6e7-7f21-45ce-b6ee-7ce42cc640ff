<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Info - FiveM UI</title>
    <link href="https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#C73659',
                        dark: {
                            100: '#262626',
                            200: '#111111',
                        },
                        light: {
                            100: '#E2E2E2',
                            200: '#D7D7D7',
                        }
                    },
                    fontFamily: {
                        'alexandria': ['Alexandria', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            /* Primary/Interact Color */
            --interact-primary: #C73659;
            --interact-secondary: #A91D3A;
            --dark-primary: #262626;
            --dark-secondary: #111111;
            --light-primary: #E2E2E2;
            --light-secondary: #D2D2D2;
            /* Fonts */
            --font-primary: 'Inter', 'PF Square Sans Pro', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            user-select: none; /* منع تحديد النص */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        body {
            font-family: 'Alexandria', sans-serif;
            background-color: transparent;
            color: var(--light-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        /* Player Counter Styles */
        .player-counter {
            position: fixed;
            top: 24px;
            left: 24px;
            z-index: 50;
            display: flex;
            align-items: center;
            gap: 12px;
            flex-direction: row-reverse;
        }

        .player-counter-number {
            color: var(--interact-primary);
            font-weight: bold;
            font-size: 3.45rem;
            text-shadow: 0px 0px 3px var(--interact-primary);
        }

        .player-counter-icon {
            color: var(--interact-primary);
            font-size: 3rem;
            text-shadow: 0px 0px 5px var(--interact-primary);
        }

        /* Player Info Styles */
        .player-info-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 40;
        }

        .connection-container {
            position: absolute;
            left: -128px;
            top: 50%;
            transform: translateY(-50%);
        }

        .id-circle {
            min-width: 16px;
            height: 16px;
            padding: 0 4px;
            background-color: var(--light-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 6px rgba(0, 0, 0, 0.873);
        }

        .id-circle span {
            color: var(--dark-primary);
            font-weight: bold;
            font-size: 0.75rem;
            white-space: nowrap;
        }

        .connection-line {
            position: absolute;
            top: 50%;
            left: calc(100% + 4px);
            width: 60px;
            height: 2px;
            background-color: var(--light-primary);
            transform: translateY(-50%);
        }

        .end-circle {
            position: absolute;
            top: 50%;
            right: -16px;
            width: 16px;
            height: 16px;
            background-color: var(--interact-primary);
            box-shadow: 0 0 5px var(--interact-primary);
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .info-sections {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 230px;
        }

        .info-section {
            border-radius: 8px;
            overflow: hidden;
            width: 100%;
        }

        .section-header {
            background-color: var(--interact-primary);
            color: #FFFFFF;
            text-align: left;
            padding: 4px 8px;
            font-size: 14px;
            margin-bottom: 4px;
            box-shadow: 0 0 4px var(--interact-primary);
        }

        .info-item {
            background-color: var(--dark-primary);
            padding: 4px 8px;
            text-align: left;
            margin-bottom: 4px;
            box-shadow: 0 0 4px 1px var(--dark-primary);
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: #F34E76;
            font-size: 13px;
            font-weight: 500;
        }

        .info-value {
            color: var(--light-primary);
            font-size: 12px;
        }

        /* Glass Effect Styles */
        .glass-effect {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(8px);
            border: none;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        .activities-container {
            position: fixed;
            top: 16px;
            right: 320px;
            z-index: 50;
        }

        .availability-container {
            position: fixed;
            top: 16px;
            right: 16px;
            z-index: 50;
        }

        .buffs-container {
            position: fixed;
            top: 200px;
            right: 16px;
            left: 16px;
            z-index: 50;
        }

        .glass-section {
            border-radius: 8px;
            overflow: hidden;
            width: 256px;
        }

        .glass-section-header {
            background: var(--interact-primary);
            color: var(--light-primary);
            font-weight: 700;
            text-align: center;
            padding: 8px 12px;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .activity-item {
            background: transparent;
            border: none;
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            background: rgba(199, 54, 89, 0.1);
            transform: translateY(-1px);
        }

        .buff-item {
            background: rgba(0, 0, 0, 0.5);
            border: none;
            transition: all 0.2s ease;
        }

        .buff-item:hover {
            background: rgba(199, 54, 89, 0.1);
        }

        .availability-item {
            background: transparent;
            border: none;
            transition: all 0.2s ease;
        }

        .availability-item:hover {
            transform: scale(1.05);
        }

        .no-buffs {
            background: rgba(0, 0, 0, 0.6);
            color: var(--light-secondary);
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }

        .bg-game {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>
<body class="font-alexandria text-light-primary">
    <main class="min-h-screen relative overflow-hidden">
        <!-- Player Counter -->
        <div class="player-counter">
            <span class="player-counter-number">1</span>
            <i class="fas fa-users player-counter-icon"></i>
        </div>

        <!-- Player Info -->
        <div class="player-info-container">
            <!-- Connection Line and Circle -->
            <div class="connection-container">
                <!-- ID Circle (White with number) -->
                <div class="id-circle">
                    <span>1000</span>
                </div>
                <!-- Line -->
                <div class="connection-line"></div>
                <!-- End Circle -->
                <div class="end-circle"></div>
            </div>

            <div class="info-sections">
                <!-- Player Info Section -->
                <div class="info-section">
                    <!-- Header -->
                    <div class="section-header">
                        SLT Dev
                    </div>

                    <!-- Content -->
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">Nationality </span><span class="info-value">USA</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Birthdate </span><span class="info-value">2000/1/1</span>
                        </div>
                    </div>
                </div>

                <!-- Details Section -->
                <div class="info-section">
                    <!-- Header -->
                    <div class="section-header">
                        Details
                    </div>

                    <!-- Content -->
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">Job </span><span class="info-value">Law Enforcement</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Play Time </span><span class="info-value">106 hours</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Paycheck </span><span class="info-value">$14320</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Bank </span><span class="info-value">$200</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Cash </span><span class="info-value">$450</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Coins </span><span class="info-value">300</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Insurance </span><span class="info-value">No Insurance</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activities (Robberies & Stores) -->
        <div class="activities-container">
            <div class="glass-effect glass-section" style="width: 280px;">
                <!-- Header -->
                <div class="glass-section-header">
                    ACTIVITIES
                </div>

                <!-- Content -->
                <div class="p-3">
                    <div class="grid grid-cols-2 gap-2">
                        <!-- Row 1 -->
                        <!-- LEO KIDNAP -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--dark-primary);">
                            <i class="fas fa-handcuffs text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">LEO KIDNAP</span>
                        </div>

                        <!-- STORES -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--interact-primary);">
                            <i class="fas fa-store text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">STORES</span>
                        </div>

                        <!-- Row 2 -->
                        <!-- HOUSES -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: #FF8C00;">
                            <i class="fas fa-home text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">HOUSES</span>
                        </div>

                        <!-- EXCHANGE -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--dark-primary);">
                            <i class="fas fa-money-bill text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">EXCHANGE</span>
                        </div>
                    </div>

                    <!-- Bottom Row - Single Item -->
                    <div class="mt-2">
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--dark-primary);">
                            <i class="fas fa-gem text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">JEWELARY</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Availability (Sectors Status) -->
        <div class="availability-container">
            <div class="glass-effect glass-section" style="width: 280px;">
                <!-- Header -->
                <div class="glass-section-header">
                    AVAILABILITY
                </div>

                <!-- Content -->
                <div class="p-3">
                    <div class="grid grid-cols-2 gap-2">
                        <!-- Row 1 -->
                        <!-- POLICE -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--interact-primary);">
                            <i class="fas fa-shield-alt text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">POLICE</span>
                        </div>

                        <!-- SHERIFF -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--dark-primary);">
                            <i class="fas fa-star text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">SHERIFF</span>
                        </div>

                        <!-- Row 2 -->
                        <!-- HOSPITAL -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--dark-primary);">
                            <i class="fas fa-heart text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">HOSPITAL</span>
                        </div>

                        <!-- Empty or another item -->
                        <div class="w-32 h-20 rounded flex flex-col items-center justify-center text-center" style="background-color: var(--dark-primary); opacity: 0.5;">
                            <i class="fas fa-question text-white text-xl mb-1"></i>
                            <span class="text-white text-xs font-medium">EMPTY</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Buffs -->
        <div class="buffs-container">
            <div class="glass-effect glass-section" style="width: 100%;">
                <!-- Header -->
                <div class="glass-section-header">
                    ACTIVE BUFFS
                </div>

                <!-- Content -->
                <div class="p-3 space-y-2">
                    <!-- Post OP Buff -->
                    <div class="flex items-center bg-gray-800 rounded overflow-hidden">
                        <div class="w-12 h-12 flex items-center justify-center" style="background-color: var(--interact-primary);">
                            <i class="fas fa-motorcycle text-white text-lg"></i>
                        </div>
                        <div class="flex-1 px-3 py-2 text-white">
                            <span class="text-sm font-medium">Post OP</span>
                        </div>
                        <div class="px-3 py-2 text-white text-sm">
                            15m
                        </div>
                        <div class="w-12 h-12 bg-black flex items-center justify-center">
                            <span class="text-sm font-bold" style="color: var(--interact-primary);">3x</span>
                        </div>
                    </div>

                    <!-- Uncle Damby's Farms Buff -->
                    <div class="flex items-center bg-gray-800 rounded overflow-hidden">
                        <div class="w-12 h-12 flex items-center justify-center" style="background-color: var(--interact-primary);">
                            <i class="fas fa-tractor text-white text-lg"></i>
                        </div>
                        <div class="flex-1 px-3 py-2 text-white">
                            <span class="text-sm font-medium">Uncle Damby's Farms</span>
                        </div>
                        <div class="px-3 py-2 text-white text-sm">
                            6m
                        </div>
                        <div class="w-12 h-12 bg-black flex items-center justify-center">
                            <span class="text-sm font-bold" style="color: var(--interact-primary);">2x</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
