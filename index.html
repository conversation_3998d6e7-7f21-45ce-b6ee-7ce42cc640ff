<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Info - FiveM UI</title>
    <link href="https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#C73659',
                        dark: {
                            100: '#262626',
                            200: '#111111',
                        },
                        light: {
                            100: '#E2E2E2',
                            200: '#D7D7D7',
                        }
                    },
                    fontFamily: {
                        'alexandria': ['Alexandria', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            /* Primary/Interact Color */
            --interact-primary: #C73659;
            --interact-secondary: #A91D3A;
            --dark-primary: #262626;
            --dark-secondary: #111111;
            --light-primary: #E2E2E2;
            --light-secondary: #D2D2D2;
            /* Fonts */
            --font-primary: 'Inter', 'PF Square Sans Pro', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            user-select: none; /* منع تحديد النص */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        body {
            font-family: 'Alexandria', sans-serif;
            background-color: transparent;
            color: var(--light-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        /* Player Counter Styles */
        .player-counter {
            position: fixed;
            top: 24px;
            left: 24px;
            z-index: 50;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-direction: row-reverse;
        }

        .player-counter-number {
            color: var(--interact-primary);
            font-weight: bold;
            font-size: 3.45rem;
            text-shadow: 0px 0px 3px var(--interact-primary);
        }

        .player-counter-icon {
            color: var(--interact-primary);
            font-size: 3rem;
            text-shadow: 0px 0px 5px var(--interact-primary);
        }

        /* Player Info Styles */
        .player-info-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 40;
        }

        .connection-container {
            position: absolute;
            left: -128px;
            top: 50%;
            transform: translateY(-50%);
        }

        .id-circle {
            min-width: 16px;
            height: 16px;
            padding: 0 4px;
            background-color: var(--light-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 6px rgba(0, 0, 0, 0.873);
        }

        .id-circle span {
            color: var(--dark-primary);
            font-weight: bold;
            font-size: 0.75rem;
            white-space: nowrap;
        }

        .connection-line {
            position: absolute;
            top: 50%;
            left: calc(100% + 4px);
            width: 60px;
            height: 2px;
            background-color: var(--light-primary);
            transform: translateY(-50%);
        }

        .end-circle {
            position: absolute;
            top: -10px;
            right: -80px;
            width: 16px;
            height: 16px;
            background-color: var(--interact-primary);
            box-shadow: 0 0 5px var(--interact-primary);
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .info-sections {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 230px;
        }

        .info-section {
            border-radius: 8px;
            overflow: hidden;
            width: 100%;
        }

        .section-header {
            background-color: var(--interact-primary);
            color: #FFFFFF;
            text-align: left;
            padding: 4px 8px;
            font-size: 13px;
            margin-bottom: 4px;
            box-shadow: 0 0 4px var(--interact-primary);
        }

        .info-item {
            background-color: var(--dark-primary);
            padding: 4px 8px;
            text-align: left;
            margin-bottom: 4px;
            box-shadow: 0 0 4px 1px var(--dark-primary);
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: #F34E76;
            font-size: 13px;
            font-weight: 500;
        }

        .info-value {
            color: var(--light-primary);
            font-size: 12px;
        }

        /* Glass Effect Styles */
        .glass-effect {
            border: none;
        }

        .activities-container {
            position: fixed;
            top: 75px;
            right: 310px;
            z-index: 50;
        }

        .availability-container {
            position: fixed;
            top: 75px;
            right: 16px;
            z-index: 50;
        }

        .grid-content {
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            justify-items: center;
        }

        .buffs-container {
            position: fixed;
            top: 415px;
            right: 16px;
            z-index: 50;
        }

        /* Activity Items */
        .activity-box {
            margin-bottom: 4px;
            width: 120px;
            height: 130px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .activity-box.unavailable {
            background-color: var(--dark-primary);
            box-shadow: 0 0 5px var(--dark-primary);
            border: 5px solid rgba(121, 112, 112, 0.3);
            border-radius: 5px;
        }

        .activity-box.available {
            background-color: var(--interact-primary);
            box-shadow: 0 0 5px var(--interact-primary);
            border: 5px solid rgba(38, 38, 38, 0.2);
            border-radius: 5px;
        }

        .activity-box.busy {
            background-color: #E9842F;
            box-shadow: 0 0 5px #E9842F;
            border: 5px solid rgba(38, 38, 38, 0.2);
            border-radius: 5px;
        }

        /* Availability Items */
        .availability-box {
            margin-bottom: 4px;
            width: 120px;
            height: 130px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .availability-box.available {
            background-color: var(--interact-primary);
            box-shadow: 0 0 5px var(--interact-primary);
            border: 5px solid rgba(38, 38, 38, 0.2);
            border-radius: 5px;
        }

        .availability-box.unavailable {
            background-color: var(--dark-primary);
            box-shadow: 0 0 5px var(--dark-primary);
            border: 5px solid rgba(121, 112, 112, 0.3);
            border-radius: 5px;
        }

        /* Buff Items */
        .buff-row {
            display: flex;
            align-items: center;
            background-color: var(--dark-primary);
            overflow: hidden;
            margin-bottom: 5px;
            border-radius: 4.5px;
        }

        .buff-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--interact-primary);
            box-shadow: 0 0 8px var(--interact-primary);
        }

        .buff-name {
            flex: 1;
            padding: 8px 12px;
            color: white;
            font-size: 15px;
        }

        .buff-time {
            padding: 8px 12px;
            color: white;
            font-size: 15px;
        }

        .buff-multiplier {
            width: 48px;
            height: 48px;
            background-color: var(--dark-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .buff-multiplier-text {
            color: var(--interact-primary);
            text-shadow: 0 0 5px var(--interact-primary);
            font-size: 18px;
        }

        /* Box Icons */
        .box-icon {
            color: white;
            font-size: 50px;
            line-height: 51px;
            margin-bottom: 15px;
        }

        /* Box Icon Images */
        .box-icon-img {
            width: 50px;
            height: 50px;
            object-fit: contain;
            margin-bottom: 15px;
            filter: brightness(0) invert(1); /* Makes images white by default */
        }

        /* Image Status Classes */
        .box-icon-img.status-available {
            filter: drop-shadow(0 0 5px #fff);
        }

        .box-icon-img.status-unavailable {
            filter: brightness(55%);
        }

        .box-icon.status-available {
            color: white;
            text-shadow: #fff 0 0 5px;
        }

        .box-icon.status-busy {
            color: white;
            text-shadow: #fff 0 0 5px;
        }

        .box-icon.status-unavailable {
            color: rgba(255, 255, 255, 0.4);
        }

        /* Box Text */
        .box-text {
            color: white;
            font-size: 12px;
            line-height: 16px;
            font-weight: 500;
            background: rgba(17, 17, 17, 0.4);
            width: 100%;
        }

        /* Buff Text */
        .buff-text {
            color: white;
            font-size: 14px;
            line-height: 20px;
            font-weight: 500;
        }

        /* Buff Time */
        .buff-time-text {
            color: white;
            font-size: 18px;
            line-height: 28px;
        }

        .glass-section {
            overflow: hidden;
            width: 256px;
        }

        .glass-section-header {
            margin-bottom: 8px;
            background: var(--interact-primary);
            box-shadow: 0 0 5px var(--interact-primary);
            border-radius: 2px;
            border: 3.5px solid rgba(38, 38, 38, 0.15);
            color: #fff;
            font-weight: 700;
            text-align: center;
            padding: 8px 12px;
            font-size: 16px;
            text-shadow: 0 0 2px #fff;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Black Glow Effect from Right */
        .black-glow-overlay {
            position: fixed;
            top: 0;
            right: 0;
            width: 100%;
            height: 100vh;
            background: linear-gradient(to left, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 30%, transparent 100%);
            pointer-events: none;
            z-index: 1;
        }
    </style>
</head>
<body class="font-alexandria text-light-primary">
    <main class="min-h-screen relative overflow-hidden">
        <!-- Black Glow Overlay -->
        <div class="black-glow-overlay"></div>

        <!-- Player Counter -->
        <div class="player-counter">
            <span class="player-counter-number">1</span>
            <i class="fas fa-users player-counter-icon"></i>
        </div>

        <!-- Player Info -->
        <div class="player-info-container">
            <!-- Connection Line and Circle -->
            <div class="connection-container">
                <!-- ID Circle (White with number) -->
                <div class="id-circle">
                    <span>1000</span>
                </div>
                <!-- Line -->
                <div class="connection-line"></div>
                <!-- End Circle -->
                <div class="end-circle"></div>
            </div>

            <div class="info-sections">
                <!-- Player Info Section -->
                <div class="info-section">
                    <!-- Header -->
                    <div class="section-header">
                        SLT Dev
                    </div>

                    <!-- Content -->
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">Nationality </span><span class="info-value">USA</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Birthdate </span><span class="info-value">2000/1/1</span>
                        </div>
                    </div>
                </div>

                <!-- Details Section -->
                <div class="info-section">
                    <!-- Header -->
                    <div class="section-header">
                        Details
                    </div>

                    <!-- Content -->
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">Job </span><span class="info-value">Law Enforcement</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Play Time </span><span class="info-value">106 hours</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Paycheck </span><span class="info-value">$14320</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Bank </span><span class="info-value">$200</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Cash </span><span class="info-value">$450</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Coins </span><span class="info-value">300</span>
                        </div>

                        <div class="info-item">
                            <span class="info-label">Insurance </span><span class="info-value">No Insurance</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activities (Robberies & Stores) -->
        <div class="activities-container">
            <div class="glass-effect glass-section">
                <!-- Header -->
                <div class="glass-section-header">
                    ACTIVITIES
                </div>

                <!-- Content -->
                <div>
                    <div class="grid-content">
                        <!-- Row 1 -->
                        <!-- LEO KIDNAP -->
                        <div class="activity-box unavailable">
                            <i class="fas fa-handcuffs box-icon status-unavailable"></i>
                            <span class="box-text">LEO KIDNAP</span>
                        </div>

                        <!-- STORES -->
                        <div class="activity-box available">
                            <i class="fa-solid fa-basket-shopping box-icon status-available"></i>
                            <span class="box-text">STORES</span>
                        </div>

                        <!-- Row 2 -->
                        <!-- HOUSES -->
                        <div class="activity-box busy">
                            <i class="fa-solid fa-house-chimney box-icon status-busy"></i>
                            <span class="box-text">HOUSES</span>
                        </div>

                        <!-- EXCHANGE -->
                        <div class="activity-box unavailable">
                            <i class="fa-solid fa-money-bill-wave box-icon status-unavailable"></i>
                            <span class="box-text">EXCHANGE</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Availability (Sectors Status) -->
        <div class="availability-container">
            <div class="glass-effect glass-section">
                <!-- Header -->
                <div class="glass-section-header">
                    AVAILABILITY
                </div>

                <!-- Content -->
                <div>
                    <div class="grid-content">
                        <!-- Row 1 -->
                        <!-- POLICE -->
                        <div class="availability-box available">
                            <img src="img/police.png" class="box-icon-img status-available" alt="Police">
                            <span class="box-text">POLICE</span>
                        </div>

                        <!-- SHERIFF -->
                        <div class="availability-box unavailable">
                            <img src="img/sheriff.png" class="box-icon-img status-unavailable" alt="Sheriff">
                            <span class="box-text">SHERIFF</span>
                        </div>

                        <!-- Row 2 -->
                        <!-- HOSPITAL -->
                        <div class="availability-box unavailable">
                            <img src="img/hospital.png" class="box-icon-img status-unavailable" alt="Hospital">
                            <span class="box-text">HOSPITAL</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Buffs -->
        <div class="buffs-container">
            <div class="glass-effect glass-section" style="width: 550px;">
                <!-- Header -->
                <div class="glass-section-header">
                    ACTIVE BUFFS
                </div>

                <!-- Content -->
                <div>
                    <!-- Post OP Buff -->
                    <div class="buff-row">
                        <div class="buff-icon">
                            <i class="fa-solid fa-truck-ramp-box buff-time-text"></i>
                        </div>
                        <div class="buff-name">
                            <span class="buff-text">Post OP</span>
                        </div>
                        <div class="buff-time">
                            <span class="buff-time-text">15m</span>
                        </div>
                        <div class="buff-multiplier">
                            <span class="buff-multiplier-text">3x</span>
                        </div>
                    </div>

                    <!-- Uncle Damby's Farms Buff -->
                    <div class="buff-row">
                        <div class="buff-icon">
                            <i class="fa-solid fa-farm buff-time-text"></i>
                        </div>
                        <div class="buff-name">
                            <span class="buff-text">Uncle Damby's Farms</span>
                        </div>
                        <div class="buff-time">
                            <span class="buff-time-text">6m</span>
                        </div>
                        <div class="buff-multiplier">
                            <span class="buff-multiplier-text">2x</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
