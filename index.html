<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Info - FiveM UI</title>
    <link href="https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#C73659',
                        dark: {
                            100: '#262626',
                            200: '#111111',
                        },
                        light: {
                            100: '#E2E2E2',
                            200: '#D7D7D7',
                        }
                    },
                    fontFamily: {
                        'alexandria': ['Alexandria', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'PF Square Sans Pro', sans-serif;
            background-color: #111111;
            color: #E2E2E2;
            overflow: hidden;
            user-select: none;
        }

        .glass-effect {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(8px);
            border: none;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        .player-info-glass {
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            border: none;
        }

        .section-header {
            background: #C73659;
            color: white;
            font-weight: 700;
            text-align: center;
            padding: 8px 12px;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .activity-item {
            background: transparent;
            border: none;
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            background: rgba(199, 54, 89, 0.1);
            transform: translateY(-1px);
        }

        .buff-item {
            background: rgba(0, 0, 0, 0.5);
            border: none;
            transition: all 0.2s ease;
        }

        .buff-item:hover {
            background: rgba(199, 54, 89, 0.1);
        }

        .availability-item {
            background: transparent;
            border: none;
            transition: all 0.2s ease;
        }

        .availability-item:hover {
            transform: scale(1.05);
        }

        .no-buffs {
            background: rgba(0, 0, 0, 0.6);
            color: #888;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }

        .bg-game {
            background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>
<body class="font-alexandria bg-dark-200 text-light-100">
    <main class="min-h-screen relative overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 bg-game">
            <div class="absolute inset-0 bg-black/50"></div>
        </div>

        <!-- Player Counter -->
        <div class="fixed top-6 left-6 z-50">
            <div class="flex items-center gap-3">
                <span class="text-primary font-bold text-6xl" style="text-shadow: 0px 0px 3px #C73659;">1</span>
                <i class="fas fa-users text-primary text-5xl" style="text-shadow: 0px 0px 5px #C73659;"></i>
            </div>
        </div>

        <!-- Player Info -->
        <div class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
            <!-- Connection Line and Circle -->
            <div class="absolute -left-32 top-1/2 transform -translate-y-1/2">
                <!-- ID Circle (White with number) -->
                <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg">
                    <span class="text-black font-bold text-lg">3</span>
                </div>
                <!-- Line -->
                <div class="absolute top-1/2 left-10 w-20 h-0.5 bg-gray-400 transform -translate-y-1/2"></div>
                <!-- End Circle -->
                <div class="absolute top-1/2 left-28 w-4 h-4 bg-primary rounded-full transform -translate-y-1/2"></div>
            </div>

            <div class="space-y-0.5">
                <!-- Player Info Section -->
                <div class="bg-black bg-opacity-80 rounded-lg overflow-hidden min-w-[320px]">
                    <!-- Header -->
                    <div class="bg-primary text-white font-bold text-center py-2 px-4 text-sm">
                        SLT Dev
                    </div>

                    <!-- Content -->
                    <div class="space-y-0">
                        <div class="bg-gray-800 px-4 py-2 border-b border-gray-700 text-left" style="border-bottom-width: 2px;">
                            <span class="text-primary text-sm font-medium">Nationality </span><span class="text-white text-sm font-medium">USA</span>
                        </div>

                        <div class="bg-gray-800 px-4 py-2 text-left">
                            <span class="text-primary text-sm font-medium">Birthdate </span><span class="text-white text-sm font-medium">2000/1/1</span>
                        </div>
                    </div>
                </div>

                <!-- Details Section -->
                <div class="bg-black bg-opacity-80 rounded-lg overflow-hidden min-w-[320px]">
                    <!-- Header -->
                    <div class="bg-primary text-white font-bold text-center py-2 px-4 text-sm">
                        Details
                    </div>

                    <!-- Content -->
                    <div class="space-y-0">
                        <div class="bg-gray-800 px-4 py-2 border-b border-gray-700 text-left" style="border-bottom-width: 2px;">
                            <span class="text-primary text-sm font-medium">Job </span><span class="text-white text-sm font-medium">Law Enforcement</span>
                        </div>

                        <div class="bg-gray-800 px-4 py-2 border-b border-gray-700 text-left" style="border-bottom-width: 2px;">
                            <span class="text-primary text-sm font-medium">Play Time </span><span class="text-white text-sm font-medium">106 hours</span>
                        </div>

                        <div class="bg-gray-800 px-4 py-2 border-b border-gray-700 text-left" style="border-bottom-width: 2px;">
                            <span class="text-primary text-sm font-medium">Paycheck </span><span class="text-white text-sm font-medium">$14320</span>
                        </div>

                        <div class="bg-gray-800 px-4 py-2 border-b border-gray-700 text-left" style="border-bottom-width: 2px;">
                            <span class="text-primary text-sm font-medium">Bank </span><span class="text-white text-sm font-medium">$200</span>
                        </div>

                        <div class="bg-gray-800 px-4 py-2 border-b border-gray-700 text-left" style="border-bottom-width: 2px;">
                            <span class="text-primary text-sm font-medium">Cash </span><span class="text-white text-sm font-medium">$450</span>
                        </div>

                        <div class="bg-gray-800 px-4 py-2 border-b border-gray-700 text-left" style="border-bottom-width: 2px;">
                            <span class="text-primary text-sm font-medium">Coins </span><span class="text-white text-sm font-medium">300</span>
                        </div>

                        <div class="bg-gray-800 px-4 py-2 text-left">
                            <span class="text-primary text-sm font-medium">Insurance </span><span class="text-white text-sm font-medium">No Insurance</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activities -->
        <div class="fixed top-4 right-4 z-50">
            <div class="glass-effect rounded-lg overflow-hidden w-64">
                <!-- Header -->
                <div class="section-header">
                    ACTIVITIES
                </div>

                <!-- Content -->
                <div class="p-2 space-y-1">
                    <!-- No activities message -->
                    <div class="no-buffs rounded text-xs">
                        No Active Activities
                    </div>
                </div>
            </div>
        </div>

        <!-- Availability -->
        <div class="fixed top-4 right-72 z-50">
            <div class="glass-effect rounded-lg overflow-hidden w-64">
                <!-- Header -->
                <div class="section-header">
                    AVAILABILITY
                </div>

                <!-- Content -->
                <div class="p-3">
                    <div class="grid grid-cols-4 gap-2">
                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #3B82F6;">
                                <i class="fas fa-handcuffs text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">LEO KIDNAP</span>
                        </div>

                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #EF4444;">
                                <i class="fas fa-store text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">STORES</span>
                        </div>

                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square opacity-50">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #10B981;">
                                <i class="fas fa-shield-alt text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">POLICE</span>
                        </div>

                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #F59E0B;">
                                <i class="fas fa-star text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">SHERIFF</span>
                        </div>

                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #F97316;">
                                <i class="fas fa-home text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">HOUSES</span>
                        </div>

                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square opacity-50">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #8B5CF6;">
                                <i class="fas fa-exchange-alt text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">EXCHANGE</span>
                        </div>

                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #06B6D4;">
                                <i class="fas fa-hospital text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">HOSPITAL</span>
                        </div>

                        <div class="availability-item rounded p-2 flex flex-col items-center gap-1 aspect-square opacity-50">
                            <div class="w-8 h-8 rounded flex items-center justify-center" style="background-color: #84CC16;">
                                <i class="fas fa-gem text-white text-sm"></i>
                            </div>
                            <span class="text-white text-xs font-medium text-center leading-tight">JEWELRY</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Buffs -->
        <div class="fixed bottom-4 right-4 z-50">
            <div class="glass-effect rounded-lg overflow-hidden w-80">
                <!-- Header -->
                <div class="section-header">
                    ACTIVE BUFFS
                </div>

                <!-- Content -->
                <div class="p-3 space-y-2">
                    <!-- No Active Buffs -->
                    <div class="no-buffs rounded text-xs">
                        No Active Buffs :(
                    </div>

                    <!-- Uncle Damby's Farms Buff -->
                    <div class="buff-item rounded p-3 flex items-center gap-3">
                        <div class="w-10 h-10 rounded flex items-center justify-center" style="background-color: #C73659;">
                            <i class="fas fa-tractor text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center">
                                <span class="text-white text-sm font-medium">Uncle Damby's Farms</span>
                                <span class="text-white text-sm">6m</span>
                            </div>
                            <div class="flex justify-end mt-1">
                                <span class="text-sm font-bold" style="color: #C73659;">2x</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
