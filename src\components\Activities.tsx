'use client'

interface Activity {
  id: string;
  name: string;
  icon: string;
  available: boolean;
}

interface ActivitiesProps {
  activities: Activity[];
}

export default function Activities({ activities }: ActivitiesProps) {
  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="glass-effect rounded-lg overflow-hidden w-72">
        {/* Header */}
        <div className="section-header">
          ACTIVITIES
        </div>

        {/* Content */}
        <div className="p-4 space-y-3">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className={`activity-item rounded-lg p-4 flex items-center gap-4 ${
                activity.available ? 'opacity-100' : 'opacity-60'
              }`}
            >
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center shadow-lg ${
                activity.available ? 'bg-green-600' : 'bg-red-600'
              }`}>
                <i className={`${activity.icon} text-white text-base`}></i>
              </div>

              <div className="flex-1">
                <span className="text-light-100 text-sm font-semibold">
                  {activity.name}
                </span>
              </div>

              <div className={`w-3 h-3 rounded-full shadow-sm ${
                activity.available ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
