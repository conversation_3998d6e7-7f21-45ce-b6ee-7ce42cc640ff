'use client'

interface Activity {
  id: string;
  name: string;
  icon: string;
  available: boolean;
}

interface ActivitiesProps {
  activities: Activity[];
}

export default function Activities({ activities }: ActivitiesProps) {
  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="glass-effect rounded-lg overflow-hidden w-64">
        {/* Header */}
        <div className="section-header">
          ACTIVITIES
        </div>

        {/* Content */}
        <div className="p-2 space-y-1">
          {/* No activities message */}
          <div className="no-buffs rounded text-xs">
            No Active Activities
          </div>
        </div>
      </div>
    </div>
  )
}
