'use client'

interface PlayerData {
  nationality: string;
  birthday: string;
  job: string;
  playtime: string;
  paycheck: number;
  bank: number;
  cash: number;
  coins: number;
  insurance: string;
}

interface PlayerInfoProps {
  playerData: PlayerData;
}

export default function PlayerInfo({ playerData }: PlayerInfoProps) {
  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
      {/* Connection Line and Circle */}
      <div className="absolute -left-32 top-1/2 transform -translate-y-1/2">
        {/* Start Circle */}
        <div className="w-6 h-6 bg-primary rounded-full"></div>
        {/* Line */}
        <div className="absolute top-1/2 left-6 w-24 h-0.5 bg-gray-400 transform -translate-y-1/2"></div>
        {/* End Circle */}
        <div className="absolute top-1/2 left-28 w-4 h-4 bg-primary rounded-full transform -translate-y-1/2"></div>
      </div>

      <div className="space-y-3">
        {/* Player Info Section */}
        <div className="bg-black bg-opacity-80 rounded-lg overflow-hidden min-w-[320px]">
          {/* Header */}
          <div className="bg-primary text-white font-bold text-center py-2 px-4 text-sm">
            SLT Dev
          </div>

          {/* Content */}
          <div className="space-y-0">
            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center border-b border-gray-700">
              <span className="text-primary text-sm font-medium">Nationality</span>
              <span className="text-white text-sm font-medium">{playerData.nationality}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center">
              <span className="text-primary text-sm font-medium">Birthdate</span>
              <span className="text-white text-sm font-medium">{playerData.birthday}</span>
            </div>
          </div>
        </div>

        {/* Details Section */}
        <div className="bg-black bg-opacity-80 rounded-lg overflow-hidden min-w-[320px]">
          {/* Header */}
          <div className="bg-primary text-white font-bold text-center py-2 px-4 text-sm">
            Details
          </div>

          {/* Content */}
          <div className="space-y-0">
            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center border-b border-gray-700">
              <span className="text-primary text-sm font-medium">Job</span>
              <span className="text-white text-sm font-medium">{playerData.job}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center border-b border-gray-700">
              <span className="text-primary text-sm font-medium">Play Time</span>
              <span className="text-white text-sm font-medium">{playerData.playtime}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center border-b border-gray-700">
              <span className="text-primary text-sm font-medium">Paycheck</span>
              <span className="text-white text-sm font-medium">${playerData.paycheck.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center border-b border-gray-700">
              <span className="text-primary text-sm font-medium">Bank</span>
              <span className="text-white text-sm font-medium">${playerData.bank.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center border-b border-gray-700">
              <span className="text-primary text-sm font-medium">Cash</span>
              <span className="text-white text-sm font-medium">${playerData.cash.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center border-b border-gray-700">
              <span className="text-primary text-sm font-medium">Coins</span>
              <span className="text-white text-sm font-medium">{playerData.coins.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex justify-between items-center">
              <span className="text-primary text-sm font-medium">Insurance</span>
              <span className="text-white text-sm font-medium">{playerData.insurance}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
