'use client'

interface PlayerData {
  nationality: string;
  birthday: string;
  job: string;
  playtime: string;
  paycheck: number;
  bank: number;
  cash: number;
  coins: number;
  insurance: string;
}

interface PlayerInfoProps {
  playerData: PlayerData;
}

export default function PlayerInfo({ playerData }: PlayerInfoProps) {
  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
      <div className="glass-effect rounded-lg overflow-hidden min-w-[320px]">
        {/* Header */}
        <div className="section-header">
          SST Dev
        </div>

        {/* Content */}
        <div className="p-5 space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Nationality:</span>
            <span className="text-light-100 font-semibold">{playerData.nationality}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Birthday:</span>
            <span className="text-light-100 font-semibold">{playerData.birthday}</span>
          </div>
        </div>

        {/* Details Section */}
        <div className="section-header">
          Details
        </div>

        <div className="p-5 space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Job:</span>
            <span className="text-light-100 font-semibold">{playerData.job}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Play Time:</span>
            <span className="text-light-100 font-semibold">{playerData.playtime}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Paycheck:</span>
            <span className="text-green-400 font-bold">${playerData.paycheck.toLocaleString()}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Bank:</span>
            <span className="text-green-400 font-bold">${playerData.bank.toLocaleString()}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Cash:</span>
            <span className="text-green-400 font-bold">${playerData.cash.toLocaleString()}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Coins:</span>
            <span className="text-yellow-400 font-bold">{playerData.coins.toLocaleString()}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-light-200 text-sm font-medium">Insurance:</span>
            <span className="text-red-400 font-semibold">{playerData.insurance}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
