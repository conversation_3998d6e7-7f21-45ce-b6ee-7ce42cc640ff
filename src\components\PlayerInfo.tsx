'use client'

interface PlayerData {
  nationality: string;
  birthday: string;
  job: string;
  playtime: string;
  paycheck: number;
  bank: number;
  cash: number;
  coins: number;
  insurance: string;
}

interface PlayerInfoProps {
  playerData: PlayerData;
  playerId?: number; // ID اللاعب للدائرة
}

export default function PlayerInfo({ playerData, playerId = 3 }: PlayerInfoProps) {
  // حساب عرض الدائرة حسب عدد الأرقام
  const getCircleWidth = (id: number) => {
    const digits = id.toString().length;
    return digits === 1 ? 'w-10 h-10' : digits === 2 ? 'w-12 h-12' : 'w-14 h-14';
  };

  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
      {/* Connection Line and Circle */}
      <div className="absolute -left-32 top-1/2 transform -translate-y-1/2">
        {/* ID Circle (White with number) */}
        <div className={`${getCircleWidth(playerId)} bg-white rounded-full flex items-center justify-center shadow-lg`}>
          <span className="text-black font-bold text-lg">{playerId}</span>
        </div>
        {/* Line */}
        <div className="absolute top-1/2 left-10 w-20 h-0.5 bg-gray-400 transform -translate-y-1/2"></div>
        {/* End Circle */}
        <div className="absolute top-1/2 left-28 w-4 h-4 bg-primary rounded-full transform -translate-y-1/2"></div>
      </div>

      <div className="space-y-0.5">
        {/* Player Info Section */}
        <div className="bg-black bg-opacity-80 rounded-lg overflow-hidden min-w-[320px]">
          {/* Header */}
          <div className="bg-primary text-white font-bold text-center py-2 px-4 text-sm">
            SLT Dev
          </div>

          {/* Content */}
          <div className="space-y-0">
            <div className="bg-gray-800 px-4 py-2 flex items-center border-b border-gray-700" style={{borderBottomWidth: '2px'}}>
              <span className="text-primary text-sm font-medium mr-2">Nationality</span>
              <span className="text-white text-sm font-medium">{playerData.nationality}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex items-center">
              <span className="text-primary text-sm font-medium mr-2">Birthdate</span>
              <span className="text-white text-sm font-medium">{playerData.birthday}</span>
            </div>
          </div>
        </div>

        {/* Details Section */}
        <div className="bg-black bg-opacity-80 rounded-lg overflow-hidden min-w-[320px]">
          {/* Header */}
          <div className="bg-primary text-white font-bold text-center py-2 px-4 text-sm">
            Details
          </div>

          {/* Content */}
          <div className="space-y-0">
            <div className="bg-gray-800 px-4 py-2 flex items-center border-b border-gray-700" style={{borderBottomWidth: '2px'}}>
              <span className="text-primary text-sm font-medium mr-2">Job</span>
              <span className="text-white text-sm font-medium">{playerData.job}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex items-center border-b border-gray-700" style={{borderBottomWidth: '2px'}}>
              <span className="text-primary text-sm font-medium mr-2">Play Time</span>
              <span className="text-white text-sm font-medium">{playerData.playtime}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex items-center border-b border-gray-700" style={{borderBottomWidth: '2px'}}>
              <span className="text-primary text-sm font-medium mr-2">Paycheck</span>
              <span className="text-white text-sm font-medium">${playerData.paycheck.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex items-center border-b border-gray-700" style={{borderBottomWidth: '2px'}}>
              <span className="text-primary text-sm font-medium mr-2">Bank</span>
              <span className="text-white text-sm font-medium">${playerData.bank.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex items-center border-b border-gray-700" style={{borderBottomWidth: '2px'}}>
              <span className="text-primary text-sm font-medium mr-2">Cash</span>
              <span className="text-white text-sm font-medium">${playerData.cash.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex items-center border-b border-gray-700" style={{borderBottomWidth: '2px'}}>
              <span className="text-primary text-sm font-medium mr-2">Coins</span>
              <span className="text-white text-sm font-medium">{playerData.coins.toLocaleString()}</span>
            </div>

            <div className="bg-gray-800 px-4 py-2 flex items-center">
              <span className="text-primary text-sm font-medium mr-2">Insurance</span>
              <span className="text-white text-sm font-medium">{playerData.insurance}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
