@import url('https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Alexandria', sans-serif;
  background-color: #111111;
  color: #E2E2E2;
  overflow: hidden;
  user-select: none;
}

.glass-effect {
  background: rgba(17, 17, 17, 0.85);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(199, 54, 89, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.section-header {
  background: #C73659;
  color: white;
  font-weight: 700;
  text-align: center;
  padding: 10px 16px;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 1px solid rgba(199, 54, 89, 0.5);
}

.activity-item {
  background: rgba(38, 38, 38, 0.7);
  border: 1px solid rgba(199, 54, 89, 0.2);
  transition: all 0.2s ease;
}

.activity-item:hover {
  border-color: rgba(199, 54, 89, 0.6);
  background: rgba(199, 54, 89, 0.1);
  transform: translateY(-1px);
}

.buff-item {
  background: rgba(38, 38, 38, 0.8);
  border-left: 4px solid #C73659;
  transition: all 0.2s ease;
}

.buff-item:hover {
  background: rgba(199, 54, 89, 0.1);
  transform: translateX(2px);
}

.availability-item {
  background: rgba(38, 38, 38, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.availability-item:hover {
  transform: scale(1.05);
  border-color: rgba(199, 54, 89, 0.4);
}
