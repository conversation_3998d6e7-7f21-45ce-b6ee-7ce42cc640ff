@import url('https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://kit-pro.fontawesome.com/releases/v6.7.2/css/pro.min.css');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Alexandria', sans-serif;
  background-color: #111111;
  color: #E2E2E2;
  overflow: hidden;
  user-select: none;
}

.glass-effect {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.player-info-glass {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border: none;
}

.section-header {
  background: #C73659;
  color: white;
  font-weight: 700;
  text-align: center;
  padding: 8px 12px;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.activity-item {
  background: transparent;
  border: none;
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: rgba(199, 54, 89, 0.1);
  transform: translateY(-1px);
}

.buff-item {
  background: rgba(0, 0, 0, 0.5);
  border: none;
  transition: all 0.2s ease;
}

.buff-item:hover {
  background: rgba(199, 54, 89, 0.1);
}

.availability-item {
  background: transparent;
  border: none;
  transition: all 0.2s ease;
}

.availability-item:hover {
  transform: scale(1.05);
}

.no-buffs {
  background: rgba(0, 0, 0, 0.6);
  color: #888;
  text-align: center;
  padding: 20px;
  font-size: 14px;
}
