/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: '#C73659',
        dark: {
          100: '#262626',
          200: '#111111',
        },
        light: {
          100: '#E2E2E2',
          200: '#D7D7D7',
        }
      },
      fontFamily: {
        'alexandria': ['Alexandria', 'sans-serif'],
        'pf-square': ['Inter', 'PF Square Sans Pro', 'sans-serif'],
        'sans': ['Inter', 'PF Square Sans Pro', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
