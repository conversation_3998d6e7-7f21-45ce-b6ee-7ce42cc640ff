# Player Info UI - Five<PERSON> Script

أفضل سكربت معلومات اللاعب لـ FiveM مع واجهة مستخدم متقدمة

## المميزات

- 🎮 عرض معلومات اللاعب الشخصية والمالية
- 📊 عداد اللاعبين المتصلين
- 🎯 عرض حالة الأنشطة (متاحة/غير متاحة)
- 🏢 عرض توفر القطاعات (شرطة، شيرف، مستشفى، إلخ)
- ⚡ عرض البافات النشطة مع المدة والمضاعف
- 🎨 تصميم داكن أنيق مع تأثيرات زجاجية
- 📱 واجهة مستجيبة ومتحركة

## التقنيات المستخدمة

- **Next.js 14** - إ<PERSON><PERSON>ر عمل React متقدم
- **TypeScript** - للكتابة الآمنة
- **Tailwind CSS** - للتصميم السريع والمرن
- **Font Awesome Pro** - للأيقونات المتقدمة
- **Alexandria Font** - خط عربي أنيق

## الألوان

- **Primary**: `#C73659` (اللون الأساسي)
- **Dark**: `#262626`, `#111111` (الخلفيات الداكنة)
- **Light**: `#E2E2E2`, `#D7D7D7` (النصوص الفاتحة)

## التثبيت والتشغيل

### المتطلبات
- Node.js 18+ 
- npm أو yarn

### خطوات التثبيت

1. **تثبيت المكتبات:**
```bash
npm install
```

2. **تشغيل الخادم المحلي:**
```bash
npm run dev
```

3. **فتح المتصفح:**
```
http://localhost:3000
```

## هيكل المشروع

```
src/
├── app/
│   ├── globals.css          # الأنماط العامة
│   ├── layout.tsx           # التخطيط الأساسي
│   └── page.tsx             # الصفحة الرئيسية
└── components/
    ├── PlayerCounter.tsx    # عداد اللاعبين
    ├── PlayerInfo.tsx       # معلومات اللاعب
    ├── Activities.tsx       # الأنشطة
    ├── Availability.tsx     # التوفر
    └── ActiveBuffs.tsx      # البافات النشطة
```

## التخصيص

يمكنك تخصيص البيانات في ملف `src/app/page.tsx`:

- `playerData`: معلومات اللاعب
- `activities`: قائمة الأنشطة
- `availabilityItems`: قائمة التوفر
- `activeBuffs`: البافات النشطة

## التطوير المستقبلي

- [ ] ربط مع FiveM API
- [ ] إضافة المزيد من الأنشطة
- [ ] نظام الإشعارات
- [ ] حفظ الإعدادات
- [ ] دعم اللغات المتعددة

## المطور

تم تطوير هذا السكربت بواسطة فريق التطوير المتخصص في FiveM

## الترخيص

هذا المشروع مخصص للاستخدام الشخصي والتعليمي
